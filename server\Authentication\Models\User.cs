namespace Final_E_Receipt.Authentication.Models
{
    public class User
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string? PasswordHash { get; set; } // Only for LOCAL auth
        public string? AzureAdObjectId { get; set; } // Only for MICROSOFT auth
        public AuthenticationType AuthType { get; set; }
        public string Role { get; set; }
        public string? OrganizationId { get; set; }
        public string? OrganizationName { get; set; }
        public bool IsActive { get; set; }
        public bool MustResetPassword { get; set; } // For LOCAL users with temp password
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLogin { get; set; }
    }
}
