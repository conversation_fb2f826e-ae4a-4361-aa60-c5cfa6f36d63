using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Notifications.DTOs
{
    public class CreateEmailConfigurationDTO
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string SmtpServer { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; } = true;
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
        public string OrganizationId { get; set; } = "SYSTEM";
    }

    public class UpdateEmailConfigurationDTO
    {
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(255)]
        public string SmtpServer { get; set; }

        [Range(1, 65535)]
        public int? Port { get; set; }

        [StringLength(255)]
        public string Username { get; set; }

        [StringLength(255)]
        public string Password { get; set; }

        public bool? EnableSsl { get; set; }

        public bool? IsDefault { get; set; }

        public bool? IsActive { get; set; }

        [StringLength(100)]
        public string SenderName { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string SenderEmail { get; set; }
    }

    public class EmailConfigurationSelectionDTO
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
    }
}
