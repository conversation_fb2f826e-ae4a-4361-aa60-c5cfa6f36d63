import { useState, useCallback } from "react";
import { apiService } from "../../services/apiService";

// Types for Email Configuration API
export interface EmailConfiguration {
  id: string;
  organizationId: string;
  smtpServer: string;
  port: number;
  username: string;
  password: string;
  enableSsl: boolean;
  isDefault: boolean;
  senderName: string;
  senderEmail: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface CreateEmailConfigurationDTO {
  name: string;
  description?: string;
  smtpServer: string;
  port: number;
  username: string;
  password: string;
  enableSsl: boolean;
  isDefault: boolean;
  isActive?: boolean;
  senderName: string;
  senderEmail: string;
  [key: string]: unknown; // Add index signature
}

export interface UpdateEmailConfigurationDTO {
  name?: string;
  description?: string;
  smtpServer?: string;
  port?: number;
  username?: string;
  password?: string;
  enableSsl?: boolean;
  isDefault?: boolean;
  isActive?: boolean;
  senderName?: string;
  senderEmail?: string;
}

// Email Configuration API Hook
export const useEmailConfigApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = useCallback(
    async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
      try {
        setLoading(true);
        setError(null);
        const result = await apiCall();
        return result;
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An error occurred");
        }
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Email configuration endpoints
  const createEmailConfig = useCallback(
    (data: CreateEmailConfigurationDTO) =>
      handleApiCall(() =>
        apiService.post<EmailConfiguration>(
          "/emailconfiguration",
          data as Record<string, unknown>
        )
      ),
    [handleApiCall]
  );

  const updateEmailConfig = useCallback(
    (id: string, data: UpdateEmailConfigurationDTO) =>
      handleApiCall(() =>
        apiService.put<EmailConfiguration>(
          `/emailconfiguration/${id}`,
          data as Record<string, unknown>
        )
      ),
    [handleApiCall]
  );

  const getEmailConfigById = useCallback(
    (id: string) =>
      handleApiCall(() =>
        apiService.get<EmailConfiguration>(`/emailconfiguration/${id}`)
      ),
    [handleApiCall]
  );

  const getEmailConfigsByOrganization = useCallback(
    (organizationId: string) =>
      handleApiCall(() =>
        apiService.get<EmailConfiguration[]>(
          `/emailconfiguration/organization/${organizationId}`
        )
      ),
    [handleApiCall]
  );

  const getDefaultEmailConfig = useCallback(
    () =>
      handleApiCall(() =>
        apiService.get<EmailConfiguration>(`/emailconfiguration/default`)
      ),
    [handleApiCall]
  );

  const deleteEmailConfig = useCallback(
    (id: string) =>
      handleApiCall(() => apiService.delete(`/emailconfiguration/${id}`)),
    [handleApiCall]
  );

  return {
    loading,
    error,
    createEmailConfig,
    updateEmailConfig,
    getEmailConfigById,
    getEmailConfigsByOrganization,
    getDefaultEmailConfig,
    deleteEmailConfig,
  };
};
