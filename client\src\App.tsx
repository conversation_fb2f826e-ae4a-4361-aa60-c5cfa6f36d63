// src/App.tsx
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { NotificationProvider } from "./contexts/NotificationContext";

// Auth Pages
import PayerLoginPage from "./pages/auth/LoginPage";
import AdminLoginPage from "./pages/auth/AdminLoginPage";
import FinanceOfficerDashboard from "./pages/FinaceOfficerDashboard";
import PayerDashboard from "./pages/PayerDashboard";
import SeniorFinanceOfficerDashboard from "./pages/SeniorFinanceOfficer";
import AdminUserManagement from "./pages/AdminDashbord";
import { MsalProvider } from "@azure/msal-react";
import { msalInstance } from "./auth/msalConfig";
import ProtectedRoute from "./components/ProctectedRoute";
import AccountSetupPage from "./pages/auth/AccountSetupPage";

function App() {
  return (
    <MsalProvider instance={msalInstance}>
      <AuthProvider>
        <NotificationProvider>
          <Router>
            <div className="App">
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<PayerLoginPage />} />
                <Route path="/adminlogin" element={<AdminLoginPage />} />
                <Route
                  path="/account-setup/:invitationId"
                  element={<AccountSetupPage />}
                />
                <Route
                  path="/authlocal/setup/:invitationId"
                  element={<AccountSetupPage />}
                />

                {/* Protected Routes with Correct Role Permissions */}
                <Route
                  path="/finance-officer-dashboard/*"
                  element={
                    <ProtectedRoute
                      allowedRoles={["FINANCE_OFFICER", "JTB_ADMIN"]}
                    >
                      <FinanceOfficerDashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/payerdashboard/*"
                  element={
                    <ProtectedRoute allowedRoles={["PAYER", "JTB_ADMIN"]}>
                      <PayerDashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/senior-finance-officer-dashboard/*"
                  element={
                    <ProtectedRoute
                      allowedRoles={["SENIOR_FINANCE_OFFICER", "JTB_ADMIN"]}
                    >
                      <SeniorFinanceOfficerDashboard />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/admin-dashboard/*"
                  element={
                    <ProtectedRoute allowedRoles={["JTB_ADMIN"]}>
                      <AdminUserManagement />
                    </ProtectedRoute>
                  }
                />

                {/* 404 Route */}
                <Route path="*" element={<div>Page Not Found</div>} />
              </Routes>
            </div>
          </Router>
        </NotificationProvider>
      </AuthProvider>
    </MsalProvider>
  );
}

export default App;
