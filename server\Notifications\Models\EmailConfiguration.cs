using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Final_E_Receipt.Notifications.Models
{
    public class EmailConfiguration
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string SmtpServer { get; set; }
        
        [Column("SmtpPort")]
        public int Port { get; set; }
        
        [Column("SmtpUsername")]
        public string Username { get; set; }
        
        [Column("SmtpPassword")]
        public string Password { get; set; }
        
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; } = true;
        public string SenderName { get; set; }
        
        [Column("FromEmail")]
        public string SenderEmail { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}
