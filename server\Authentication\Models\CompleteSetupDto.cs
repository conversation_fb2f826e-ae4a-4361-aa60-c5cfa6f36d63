using System.ComponentModel.DataAnnotations;

namespace Final_E_Receipt.Authentication.Models
{
    public class CompleteSetupDto
    {
        [Required]
        public string InvitationId { get; set; } = string.Empty;
        
        [Required]
        public string TemporaryPassword { get; set; } = string.Empty;
        
        [Required]
        public string FirstName { get; set; } = string.Empty;
        
        [Required]
        public string LastName { get; set; } = string.Empty;
        
        [Required]
        [MinLength(8)]
        public string NewPassword { get; set; } = string.Empty;
    }
}
