using Final_E_Receipt.Authentication.Services;
using Final_E_Receipt.Infrastructure.Swagger;
using Final_E_Receipt.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Identity.Web;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Reflection;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Enable detailed error information for token validation
Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;

// Add controllers first
builder.Services.AddControllers();

// Add API Explorer (required for Swagger)
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger/OpenAPI
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "Final E-Receipt API",
        Description = "A comprehensive API for electronic receipt management",
        Contact = new OpenApiContact
        {
            Name = "Support",
            Email = "<EMAIL>"
        }
    });

    // Improved JWT Configuration
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Type = SecuritySchemeType.Http, // Changed from ApiKey to Http
        Scheme = "bearer", // Lowercase
        BearerFormat = "JWT",
        Description = @"JWT Authorization header using the Bearer scheme.
                      Enter 'Bearer' [space] and then your token.
                      Example: 'Bearer 12345abcdef'"
    });

    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Only include XML comments if file exists
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        options.IncludeXmlComments(xmlPath);
    }
    else
    {
        Console.WriteLine($"Warning: XML documentation file not found at {xmlPath}");
    }
});
// Configure Authentication (Consolidated - Single configuration)
var authBuilder = builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = "MultiScheme";
    options.DefaultChallengeScheme = "MultiScheme";
});

// Add Microsoft Identity Web API authentication
authBuilder.AddMicrosoftIdentityWebApi(jwtOptions =>
{
    jwtOptions.TokenValidationParameters.ValidateIssuer = true;
    jwtOptions.TokenValidationParameters.ValidateAudience = true;
    jwtOptions.TokenValidationParameters.ValidateLifetime = true;
    jwtOptions.TokenValidationParameters.ValidateIssuerSigningKey = true;
    jwtOptions.TokenValidationParameters.NameClaimType = "preferred_username";
    jwtOptions.TokenValidationParameters.RoleClaimType = "roles";
},
microsoftIdentityOptions =>

{
    builder.Configuration.Bind("AzureAd", microsoftIdentityOptions);
    if (string.IsNullOrEmpty(microsoftIdentityOptions.Instance))
    {
        microsoftIdentityOptions.Instance = "https://login.microsoftonline.com/";
    }
}, "Microsoft");

// Add Local JWT Bearer authentication
authBuilder.AddJwtBearer("Local", options =>
{
    var jwtSettings = builder.Configuration.GetSection("JwtSettings");
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(jwtSettings["SecretKey"] ?? "DefaultSecretKey123456789"))
    };

    // Configure JWT token reading from cookies and headers
    options.Events = new JwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();

            // 1. First check Authorization header (for Swagger/Postman)
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader?.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase) == true)
            {
                context.Token = authHeader.Substring("Bearer ".Length).Trim();
                logger.LogInformation("JWT: Token found in Authorization header");
                return Task.CompletedTask;
            }

            // 2. Then check cookies (for browser requests)
            if (context.Request.Cookies.TryGetValue("authToken", out var cookieToken))
            {
                context.Token = cookieToken;
                logger.LogInformation("JWT: Token found in cookie");
            }

            return Task.CompletedTask;
        },
        OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogError($"JWT Authentication failed: {context.Exception}");
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogInformation($"JWT: Token validated for {context.Principal?.Identity?.Name}");
            return Task.CompletedTask;
        }
    };
});

// Add Policy Scheme for multi-authentication
authBuilder.AddPolicyScheme("MultiScheme", "Multi Scheme", options =>
{
    options.ForwardDefaultSelector = context =>
    {
        var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
        var path = context.Request.Path.Value?.ToLower();
        var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
        var authTypeHeader = context.Request.Headers["X-Auth-Type"].FirstOrDefault();

        // 1. FIRST check for explicit auth type header (PRIORITY)
        if (!string.IsNullOrEmpty(authTypeHeader))
        {
            var scheme = authTypeHeader.ToLower() == "local" ? "Local" : "Microsoft";
            logger.LogInformation($"Using explicit auth type: {authTypeHeader} -> {scheme}");
            return scheme;
        }

        // 2. Check path patterns for local auth
        if (path.Contains("/api/auth/local") || path.Contains("/local"))
        {
            logger.LogInformation($"Path-based selection: Local scheme for {path}");
            return "Local";
        }

        // 3. Check path patterns for Microsoft auth
        if (path.Contains("/api/auth/microsoft") || path.Contains("/microsoft"))
        {
            logger.LogInformation($"Path-based selection: Microsoft scheme for {path}");
            return "Microsoft";
        }

        // 4. Token-based detection (when Authorization header exists)
        if (authHeader?.StartsWith("Bearer ") == true)
        {
            var token = authHeader.Substring("Bearer ".Length).Trim();

            // Local tokens are typically shorter (< 500 chars)
            if (token.Length < 500)
            {
                logger.LogInformation("Token-based selection: Local (short token)");
                return "Local";
            }

            logger.LogInformation("Token-based selection: Microsoft (long token)");
            return "Microsoft";
        }

        // 5. Default to Local for API endpoints, Microsoft for others
        if (path.StartsWith("/api/"))
        {
            logger.LogInformation("Defaulting to Local for API endpoint");
            return "Local";
        }

        logger.LogInformation("Defaulting to Microsoft");
        return "Microsoft";
    };
});

// Add authorization with policies
builder.Services.AddAuthorization(options =>
{
    // Admin policy
    options.AddPolicy("RequireJTBAdmin", policy =>
        policy.RequireRole("JTB_ADMIN")
              .AddAuthenticationSchemes("Local", "Microsoft"));

    // Payer policy
    options.AddPolicy("RequirePayer", policy =>
        policy.RequireRole("PAYER")
              .AddAuthenticationSchemes("Local", "Microsoft"));

    // Combined officer policy
    options.AddPolicy("RequireOfficer", policy =>
        policy.RequireRole("FINANCE_OFFICER")
              .AddAuthenticationSchemes("Local", "Microsoft"));

    // Senior Officer-only policy
    options.AddPolicy("RequireSeniorOfficer", policy =>
        policy.RequireRole("SENIOR_FINANCE_OFFICER")
              .AddAuthenticationSchemes("Local", "Microsoft"));
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5173")
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials();
    });
});

// Register application services
builder.Services.AddSingleton<IDatabaseService, DatabaseService>();
builder.Services.AddScoped<AuthenticationService>();
builder.Services.AddScoped<Final_E_Receipt.Organizations.Services.OrganizationService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentApprovalService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentProofService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentScheduleService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentScheduleImportService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentComplianceService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentTypeService>();
builder.Services.AddScoped<Final_E_Receipt.Payments.Services.PaymentProfileService>();
builder.Services.AddScoped<Final_E_Receipt.Receipts.Services.ReceiptService>();
builder.Services.AddScoped<Final_E_Receipt.Receipts.Services.ReceiptFileService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ReportingService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ReportExportService>();
builder.Services.AddScoped<Final_E_Receipt.Notifications.Services.CentralizedEmailService>();
builder.Services.AddScoped<Final_E_Receipt.Notifications.Services.EmailConfigurationService>();
builder.Services.AddScoped<Final_E_Receipt.Notifications.Services.EmailTemplateService>();
builder.Services.AddScoped<Final_E_Receipt.Notifications.Services.UserEmailService>();
builder.Services.AddScoped<Final_E_Receipt.Notifications.Services.NotificationManagementService>();
builder.Services.AddScoped<Final_E_Receipt.Files.Services.FileService>();
builder.Services.AddScoped<Final_E_Receipt.Compliance.Services.ComplianceCertificateService>();
builder.Services.AddScoped<Final_E_Receipt.Compliance.Services.ComplianceCertificateFileService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ComplianceReportingService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ReportExcelService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ReportCsvService>();
builder.Services.AddScoped<Final_E_Receipt.Reporting.Services.ReportCacheService>();
builder.Services.AddScoped<Final_E_Receipt.Organizations.Services.OrganizationComplianceService>();
builder.Services.AddScoped<Final_E_Receipt.Receipts.Services.ReceiptTemplateService>();
builder.Services.AddScoped<Final_E_Receipt.Documents.Services.BrandedDocumentService>();
builder.Services.AddScoped<Final_E_Receipt.Documents.Services.CertificateTemplateService>();
builder.Services.AddScoped<Final_E_Receipt.Common.Services.AuditService>();
builder.Services.AddMemoryCache();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();

    // Configure Swagger
    app.UseSwagger(c =>
    {
        c.RouteTemplate = "swagger/{documentName}/swagger.json";
    });

    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Final E-Receipt API V1");
        c.RoutePrefix = "swagger"; // Access at /swagger
        c.DisplayRequestDuration();
        c.EnableTryItOutByDefault();
    });
}

app.UseHttpsRedirection();

// Enable static files for uploads
app.UseStaticFiles();

// Apply CORS policy
app.UseCors("AllowReactApp");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Map controllers
app.MapControllers();

app.Run();
