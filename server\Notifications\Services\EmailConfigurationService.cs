using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Services;
using Dapper;

namespace Final_E_Receipt.Notifications.Services
{
    public class EmailConfigurationService
    {
        private readonly IDatabaseService _dbService;

        public EmailConfigurationService(IDatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<EmailConfiguration> CreateEmailConfiguration(EmailConfiguration config)
        {
            var parameters = new
            {
                Id = config.Id ?? Guid.NewGuid().ToString(),
                Name = config.Name,
                Description = config.Description,
                SmtpServer = config.SmtpServer,
                Port = config.Port,
                Username = config.Username,
                Password = config.Password,
                EnableSsl = config.EnableSsl,
                IsDefault = config.IsDefault,
                IsActive = config.IsActive,
                SenderName = config.SenderName,
                SenderEmail = config.SenderEmail,
                CreatedBy = config.CreatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<EmailConfiguration>(
                "CreateEmailConfiguration", parameters);
        }

        public async Task<EmailConfiguration> GetEmailConfigurationById(string id)
        {
            var parameters = new { Id = id };

            return await _dbService.QueryFirstOrDefaultAsync<EmailConfiguration>(
                "GetEmailConfigurationById", parameters);
        }

        public async Task<List<EmailConfiguration>> GetAllEmailConfigurations()
        {
            var configs = await _dbService.QueryAsync<EmailConfiguration>("GetAllEmailConfigurations");
            return configs.ToList();
        }

        public async Task<List<EmailConfiguration>> GetActiveEmailConfigurations()
        {
            var configs = await _dbService.QueryAsync<EmailConfiguration>("GetActiveEmailConfigurations");
            return configs.ToList();
        }

        public async Task<EmailConfiguration> GetDefaultEmailConfiguration()
        {
            return await _dbService.QueryFirstOrDefaultAsync<EmailConfiguration>(
                "GetDefaultEmailConfiguration");
        }

        public async Task<EmailConfiguration> UpdateEmailConfiguration(EmailConfiguration config)
        {
            var parameters = new
            {
                Id = config.Id,
                SmtpServer = config.SmtpServer,
                Port = config.Port,
                Username = config.Username,
                Password = config.Password,
                EnableSsl = config.EnableSsl,
                IsDefault = config.IsDefault,
                SenderName = config.SenderName,
                SenderEmail = config.SenderEmail,
                UpdatedBy = config.UpdatedBy
            };

            return await _dbService.QueryFirstOrDefaultAsync<EmailConfiguration>(
                "UpdateEmailConfiguration", parameters);
        }

        public async Task<bool> DeleteEmailConfiguration(string id)
        {
            var parameters = new { Id = id };

            await _dbService.ExecuteAsync("DeleteEmailConfiguration", parameters);
            return true;
        }
    }
}
