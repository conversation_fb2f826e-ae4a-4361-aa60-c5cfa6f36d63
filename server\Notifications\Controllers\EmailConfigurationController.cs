using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Notifications.DTOs;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Notifications.Services;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Notifications.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    // Remove the [Authorize] attribute from the class level
    public class EmailConfigurationController : ControllerBase
    {
        private readonly EmailConfigurationService _emailConfigService;
        private readonly ILogger<EmailConfigurationController> _logger;

        public EmailConfigurationController(
            EmailConfigurationService emailConfigService, 
            ILogger<EmailConfigurationController> logger)
        {
            _emailConfigService = emailConfigService;
            _logger = logger;
        }

        [HttpPost]
        // No authorization for testing
        public async Task<IActionResult> CreateEmailConfiguration([FromBody] CreateEmailConfigurationDTO dto)
        {
            try
            {
                _logger.LogInformation($"Received DTO: Name={dto?.Name}, SmtpServer={dto?.SmtpServer}, Port={dto?.Port}");

                if (dto == null)
                    return BadRequest(new { message = "Request body is null" });

                if (string.IsNullOrEmpty(dto.Name))
                    return BadRequest(new { message = "Name is required" });
                
                if (string.IsNullOrEmpty(dto.SmtpServer))
                    return BadRequest(new { message = "SMTP Server is required" });

                var config = new EmailConfiguration
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = dto.Name,
                    Description = dto.Description,
                    SmtpServer = dto.SmtpServer,
                    Port = dto.Port,
                    Username = dto.Username,
                    Password = dto.Password,
                    EnableSsl = dto.EnableSsl,
                    IsDefault = dto.IsDefault,
                    IsActive = dto.IsActive,
                    SenderName = dto.SenderName,
                    SenderEmail = dto.SenderEmail,
                    CreatedBy = "SYSTEM"
                };

                var createdConfig = await _emailConfigService.CreateEmailConfiguration(config);
                
                if (createdConfig == null)
                    return BadRequest(new { message = "Failed to create email configuration" });

                return Ok(createdConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email configuration");
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpGet("organization/{organizationId}")]
       // [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailConfigurationsByOrganization(string organizationId)
        {
            var configs = await _emailConfigService.GetActiveEmailConfigurations();
            return Ok(configs);
        }

        [HttpGet("{id}")]
        //[Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailConfigurationById(string id)
        {
            var config = await _emailConfigService.GetEmailConfigurationById(id);
            
            if (config == null)
                return NotFound(new { message = "Email configuration not found" });

            return Ok(config);
        }

        [HttpGet("default")]
        [AllowAnonymous] // Temporary for testing
        public async Task<IActionResult> GetDefaultEmailConfiguration()
        {
            try
            {
                var config = await _emailConfigService.GetDefaultEmailConfiguration();
                
                if (config == null)
                    return NotFound(new { message = "Default email configuration not found" });

                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting default email configuration");
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpPut("{id}")]
        //[Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateEmailConfiguration(string id, [FromBody] UpdateEmailConfigurationDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var existingConfig = await _emailConfigService.GetEmailConfigurationById(id);
            if (existingConfig == null)
                return NotFound(new { message = "Email configuration not found" });

            // Only update properties that are provided (not null)
            if (!string.IsNullOrEmpty(dto.Name))
                existingConfig.Name = dto.Name;
            if (!string.IsNullOrEmpty(dto.Description))
                existingConfig.Description = dto.Description;
            if (!string.IsNullOrEmpty(dto.SmtpServer))
                existingConfig.SmtpServer = dto.SmtpServer;
            if (dto.Port.HasValue)
                existingConfig.Port = dto.Port.Value;
            if (!string.IsNullOrEmpty(dto.Username))
                existingConfig.Username = dto.Username;
            if (!string.IsNullOrEmpty(dto.Password))
                existingConfig.Password = dto.Password;
            if (dto.EnableSsl.HasValue)
                existingConfig.EnableSsl = dto.EnableSsl.Value;
            if (dto.IsDefault.HasValue)
                existingConfig.IsDefault = dto.IsDefault.Value;
            if (dto.IsActive.HasValue)
                existingConfig.IsActive = dto.IsActive.Value;
            if (!string.IsNullOrEmpty(dto.SenderName))
                existingConfig.SenderName = dto.SenderName;
            if (!string.IsNullOrEmpty(dto.SenderEmail))
                existingConfig.SenderEmail = dto.SenderEmail;

            existingConfig.UpdatedBy = userId ?? "SYSTEM";

            var updatedConfig = await _emailConfigService.UpdateEmailConfiguration(existingConfig);
            
            if (updatedConfig == null)
                return BadRequest(new { message = "Failed to update email configuration" });

            return Ok(updatedConfig);
        }

        [HttpDelete("{id}")]
        //[Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteEmailConfiguration(string id)
        {
            var existingConfig = await _emailConfigService.GetEmailConfigurationById(id);
            if (existingConfig == null)
                return NotFound(new { message = "Email configuration not found" });

            var result = await _emailConfigService.DeleteEmailConfiguration(id);
            
            if (!result)
                return BadRequest(new { message = "Failed to delete email configuration" });

            return Ok(new { message = "Email configuration deleted successfully" });
        }
    }
}
